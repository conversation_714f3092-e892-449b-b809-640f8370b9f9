package fr.enedis.i2r.system.watchdog;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DeadlockDetector {

    private static final Logger logger = LoggerFactory.getLogger(DeadlockDetector.class);
    private static final ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();

    public static boolean hasDeadlocks() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        return deadlockedThreads != null && deadlockedThreads.length > 0;
    }

    public static boolean checkAndLogDeadlocks() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();

        if (deadlockedThreads != null && deadlockedThreads.length > 0) {
            logger.error("DEADLOCK DETECTED! {} threads involved: {}",
                deadlockedThreads.length, Arrays.toString(deadlockedThreads));

            var threadInfos = threadBean.getThreadInfo(deadlockedThreads);
            for (var threadInfo : threadInfos) {
                if (threadInfo != null) {
                    logger.error("Deadlocked thread: {} (ID: {}) - State: {} - Blocked on: {}",
                        threadInfo.getThreadName(),
                        threadInfo.getThreadId(),
                        threadInfo.getThreadState(),
                        threadInfo.getLockName());
                }
            }
            return true;
        }

        return false;
    }

    public static String getDeadlockStatus() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();

        if (deadlockedThreads == null || deadlockedThreads.length == 0) {
            return "No deadlocks detected";
        } else {
            return String.format("DEADLOCK: %d threads deadlocked", deadlockedThreads.length);
        }
    }
}
