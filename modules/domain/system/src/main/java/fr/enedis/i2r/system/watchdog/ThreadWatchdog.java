package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ThreadWatchdog {

    private static final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private static final ConcurrentMap<String, Instant> threads = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, ScheduledFuture<?>> scheduledHeartbeats = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private static Duration defaultHeartbeatInterval = Duration.ofSeconds(45);

    public static void setDefaultHeartbeatInterval(Duration interval) {
        defaultHeartbeatInterval = interval;
    }

    public static void register(String threadName) {
        threads.put(threadName, Instant.now());
        startHeartbeat(threadName);
        logger.info("Thread registered for monitoring: {}", threadName);
    }

    private static void startHeartbeat(String threadName) {
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
            () -> CompletableFuture.runAsync(() -> heartbeat(threadName)),
            defaultHeartbeatInterval.toSeconds(),
            defaultHeartbeatInterval.toSeconds(),
            TimeUnit.SECONDS
        );
        scheduledHeartbeats.put(threadName, future);
        logger.info("Heartbeat started for thread: {} with interval: {}s", threadName, defaultHeartbeatInterval.toSeconds());
    }

    private static void heartbeat(String threadName) {
        Instant lastHeartbeat = threads.get(threadName);
        if (lastHeartbeat != null) {
            threads.put(threadName, Instant.now());
            logger.debug("Heartbeat recorded for thread: {} at {}", threadName, Instant.now());
        } else {
            logger.warn("Heartbeat for unregistered thread: {}", threadName);
        }
    }

    public static void unregister(String threadName) {
        Instant removed = threads.remove(threadName);
        ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
        if (future != null) {
            future.cancel(false);
            logger.info("Heartbeat stopped for thread: {}", threadName);
        }
        if (removed != null) {
            logger.info("Thread unregistered from monitoring: {}", threadName);
        }
    }

    public static void clearAll() {
        threads.clear();
        scheduledHeartbeats.values().forEach(future -> future.cancel(false));
        scheduledHeartbeats.clear();
        logger.debug("All threads cleared from monitoring");
    }

    public static void shutdown() {
        clearAll();
        scheduler.shutdown();
        logger.info("ThreadWatchdog scheduler shutdown");
    }
}
