package fr.enedis.i2r.system.systemd;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.WatchdogSocketPort;
import fr.enedis.i2r.system.watchdog.DeadlockDetector;

public class WatchdogService implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(WatchdogService.class);

    private final WatchdogSocketPort notifySocket;
    private final SystemConfiguration configuration;
    private volatile boolean running = true;

    public WatchdogService(WatchdogSocketPort notifySocket, SystemConfiguration configuration) {
        this.notifySocket = notifySocket;
        this.configuration = configuration;
    }

    @Override
    public void run() {
        try {
            logger.info("Service Watchdog démarré");
            notifySocket.init();

            while (running && !Thread.currentThread().isInterrupted()) {
                // Send heartbeat to systemd
                notifySocket.heartbeat();

                // Check for deadlocks
                if (DeadlockDetector.checkAndLogDeadlocks()) {
                    logger.error("CRITICAL: Deadlock detected in application!");
                }

                logger.debug("Watchdog monitoring active");
                Thread.sleep(configuration.watchdogPeriod());
            }
        } catch (InterruptedException e) {
            logger.info("Watchdog service interrupted");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("Erreur pendant le démarrage du service Watchdog", e);
        } finally {
            logger.info("Watchdog service stopped");
        }
    }

    public void stop() {
        logger.info("Stopping watchdog service...");
        running = false;
    }
}
