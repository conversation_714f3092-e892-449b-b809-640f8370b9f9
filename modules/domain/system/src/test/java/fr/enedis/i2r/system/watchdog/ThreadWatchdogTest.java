package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ThreadWatchdogTest {

    @BeforeEach
    void setUp() {
        ThreadWatchdog.clearAll();
    }

    @AfterEach
    void tearDown() {
        ThreadWatchdog.clearAll();
    }

    @Test
    void un_thread_est_bien_enregistre_et_desenregistre() {
        String threadName = "test-thread";

        assertDoesNotThrow(() -> ThreadWatchdog.register(threadName));
        assertDoesNotThrow(() -> ThreadWatchdog.unregister(threadName));
    }

    @Test
    void plusieurs_threads_sont_bien_enregistres_et_desenregistres() {
        assertDoesNotThrow(() -> {
            ThreadWatchdog.register("thread1");
            ThreadWatchdog.register("thread2");
            ThreadWatchdog.register("thread3");

            ThreadWatchdog.unregister("thread1");
            ThreadWatchdog.unregister("thread2");
            ThreadWatchdog.unregister("thread3");
        });
    }
}
